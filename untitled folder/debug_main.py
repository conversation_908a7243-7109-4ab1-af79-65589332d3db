#!/usr/bin/env python3
import sys
import traceback
from pathlib import Path

# Global fix for PyTorch 2.6+ compatibility with YOLO models - MUST BE FIRST
import torch
import torch.serialization

print("正在设置PyTorch补丁...")

# 强制设置weights_only=False作为备用方案
_original_torch_load = torch.load
def _patched_torch_load(*args, **kwargs):
    if 'weights_only' not in kwargs:
        kwargs['weights_only'] = False
    return _original_torch_load(*args, **kwargs)
torch.load = _patched_torch_load

# 添加安全全局变量
try:
    from ultralytics.nn.tasks import SegmentationModel
    torch.serialization.add_safe_globals([SegmentationModel])
except ImportError:
    pass

print("正在导入其他依赖...")
import pickle
from ultralytics import YOLO

print("导入完成，开始测试...")

def test_step_by_step():
    print("\n=== 步骤1: 测试路径 ===")
    base_dir = Path('/Users/<USER>/Desktop/pose_matching_project')
    results_dir = Path('/Users/<USER>/Desktop/results')
    human_model_path = base_dir / "models" / "human_seg_model.pt"
    animal_db_path = base_dir / "database" / "animal_contours.pkl"
    
    print(f"基础目录: {base_dir} (存在: {base_dir.exists()})")
    print(f"结果目录: {results_dir} (存在: {results_dir.exists()})")
    print(f"模型文件: {human_model_path} (存在: {human_model_path.exists()})")
    print(f"数据库文件: {animal_db_path} (存在: {animal_db_path.exists()})")
    
    print("\n=== 步骤2: 创建结果目录 ===")
    results_dir.mkdir(parents=True, exist_ok=True)
    print("结果目录创建完成")
    
    print("\n=== 步骤3: 加载YOLO模型 ===")
    try:
        human_model = YOLO(str(human_model_path))
        print("YOLO模型加载成功!")
    except Exception as e:
        print(f"YOLO模型加载失败: {e}")
        traceback.print_exc()
        return False
    
    print("\n=== 步骤4: 加载动物数据库 ===")
    try:
        with open(animal_db_path, 'rb') as f:
            animal_db = pickle.load(f)
        print(f"动物数据库加载成功! 包含 {len(animal_db)} 个条目")
    except Exception as e:
        print(f"动物数据库加载失败: {e}")
        traceback.print_exc()
        return False
    
    print("\n=== 所有测试通过! ===")
    return True

if __name__ == "__main__":
    test_step_by_step() 
#!/usr/bin/env python3
"""
动物姿态匹配器主程序
整合所有组件，提供完整的Web应用程序
"""

import sys
import argparse
import traceback
import threading
import socketserver
import http.server
from pathlib import Path

# 导入工具函数
from backend.utils.helpers import setup_torch_compatibility, log_system_info, find_available_port

# 设置PyTorch兼容性（必须在导入其他模块之前）
setup_torch_compatibility()

# 导入我们的模块
from backend.retrieval_system import RetrievalSystem
from backend.api.routes import RouteHandler
from backend.api.animal_info import AnimalInfoAPI
from backend.utils.template_manager import TemplateManager


class AnimalPoseMatcherServer:
    """动物姿态匹配器Web服务器"""
    
    def __init__(self, port=8080):
        """初始化服务器"""
        self.port = port
        self.server = None
        self.server_thread = None
        
        # 设置路径
        self.project_dir = Path(__file__).parent
        self.template_dir = self.project_dir / 'frontend' / 'templates'
        
        print("初始化动物姿态匹配器服务器...")
        
        # 初始化组件
        self._initialize_components()
        
        print(f"服务器初始化完成，端口: {self.port}")

    def _initialize_components(self):
        """初始化所有组件"""
        try:
            # 初始化检索系统
            print("初始化检索系统...")
            self.retrieval_system = RetrievalSystem()
            
            # 初始化模板管理器
            print("初始化模板管理器...")
            self.template_manager = TemplateManager(self.template_dir)
            
            # 初始化动物信息API
            print("初始化动物信息API...")
            self.animal_info_api = AnimalInfoAPI()
            
            # 初始化路由处理器
            print("初始化路由处理器...")
            self.route_handler = RouteHandler(
                self.retrieval_system,
                self.template_manager, 
                self.animal_info_api
            )
            
            print("所有组件初始化完成")
            
        except Exception as e:
            print(f"组件初始化失败: {e}")
            traceback.print_exc()
            raise

    def create_request_handler(self):
        """创建HTTP请求处理器"""
        route_handler = self.route_handler
        project_dir = self.project_dir
        
        class RequestHandler(http.server.SimpleHTTPRequestHandler):
            """自定义HTTP请求处理器"""
            
            def __init__(self, *args, **kwargs):
                # 设置静态文件目录
                super().__init__(*args, directory=str(project_dir), **kwargs)
            
            def log_message(self, format, *args):
                """禁用不必要的日志"""
                # 只记录错误
                if '404' in str(args) or '500' in str(args):
                    print(f"HTTP错误: {format % args}")
            
            def do_GET(self):
                """处理GET请求"""
                try:
                    import urllib.parse
                    parsed_path = urllib.parse.urlparse(self.path)
                    query_params = urllib.parse.parse_qs(parsed_path.query)
                    
                    # 处理静态文件
                    if self.path.startswith('/static/'):
                        self._serve_static_file()
                        return
                    
                    # 交给路由处理器处理
                    route_handler.handle_get_request(self, parsed_path, query_params)
                    
                except Exception as e:
                    print(f"处理GET请求时出错: {e}")
                    traceback.print_exc()
                    self.send_error(500, f"Internal server error: {str(e)}")
            
            def do_POST(self):
                """处理POST请求"""
                try:
                    route_handler.handle_post_request(self)
                except Exception as e:
                    print(f"处理POST请求时出错: {e}")
                    traceback.print_exc()
                    self.send_error(500, f"Internal server error: {str(e)}")
            
            def _serve_static_file(self):
                """提供静态文件服务"""
                try:
                    # 移除'/static'前缀，映射到frontend/static
                    static_path = self.path[8:]  # 移除'/static/'
                    full_path = project_dir / 'frontend' / 'static' / static_path.lstrip('/')
                    
                    if full_path.exists() and full_path.is_file():
                        self.send_response(200)
                        
                        # 设置Content-Type
                        if full_path.suffix == '.css':
                            self.send_header('Content-type', 'text/css')
                        elif full_path.suffix == '.js':
                            self.send_header('Content-type', 'application/javascript')
                        elif full_path.suffix in ['.jpg', '.jpeg']:
                            self.send_header('Content-type', 'image/jpeg')
                        elif full_path.suffix == '.png':
                            self.send_header('Content-type', 'image/png')
                        else:
                            self.send_header('Content-type', 'application/octet-stream')
                            
                        self.end_headers()
                        
                        with open(full_path, 'rb') as f:
                            self.wfile.write(f.read())
                    else:
                        self.send_error(404, f"Static file not found: {static_path}")
                        
                except Exception as e:
                    print(f"提供静态文件时出错: {e}")
                    self.send_error(500, f"Static file service error: {str(e)}")
        
        return RequestHandler

    def start(self):
        """启动Web服务器"""
        try:
            # 检查端口是否可用
            if not self._check_port():
                return False
                
            # 创建HTTP服务器
            handler_class = self.create_request_handler()
            self.server = socketserver.TCPServer(("", self.port), handler_class)
            
            # 在单独线程中启动服务器
            self.server_thread = threading.Thread(target=self.server.serve_forever)
            self.server_thread.daemon = True
            self.server_thread.start()
            
            print(f"\n动物姿态匹配器服务器已启动")
            print(f"访问地址: http://localhost:{self.port}")
            print("按 Ctrl+C 停止服务器")
            
            # 尝试打开浏览器
            self._open_browser()
            
            # 等待服务器线程
            try:
                while True:
                    import time
                    time.sleep(1)
            except KeyboardInterrupt:
                print("\n正在关闭服务器...")
                self.stop()
                
            return True
            
        except Exception as e:
            print(f"启动服务器失败: {e}")
            traceback.print_exc()
            return False

    def _check_port(self):
        """检查端口是否可用"""
        if not find_available_port(self.port, 1):
            print(f"端口 {self.port} 被占用")
            # 尝试找到可用端口
            available_port = find_available_port(self.port + 1, 10)
            if available_port:
                print(f"使用可用端口: {available_port}")
                self.port = available_port
                return True
            else:
                print("未找到可用端口")
                return False
        return True

    def _open_browser(self):
        """打开浏览器"""
        try:
            import webbrowser
            import time
            # 延迟一点时间确保服务器启动
            threading.Timer(1.0, lambda: webbrowser.open(f"http://localhost:{self.port}")).start()
        except Exception as e:
            print(f"无法打开浏览器: {e}")

    def stop(self):
        """停止服务器"""
        if self.server:
            self.server.shutdown()
            if self.server_thread:
                self.server_thread.join()
            print("服务器已停止")


class CameraCapture:
    """摄像头拍照功能（用于OpenCV模式）"""
    
    def __init__(self, retrieval_system):
        """初始化摄像头拍照系统"""
        self.retrieval_system = retrieval_system
        
    def capture_and_retrieve(self):
        """开始摄像头拍照流程"""
        try:
            import cv2
            import time
            
            cap = cv2.VideoCapture(0)
            
            if not cap.isOpened():
                print("无法打开摄像头")
                return
                
            print("按空格键开始拍照倒计时，按'q'退出")
            
            while True:
                ret, frame = cap.read()
                if not ret:
                    break
                    
                # 显示提示文字
                cv2.putText(frame, "Press SPACE to start countdown", (50, 50),
                          cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
                cv2.putText(frame, "Press 'q' to quit", (50, 100),
                          cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
                
                cv2.imshow('Animal Pose Matcher', frame)
                
                key = cv2.waitKey(1) & 0xFF
                if key == ord('q'):
                    break
                elif key == ord(' '):
                    # 开始倒计时
                    self._countdown_and_capture(cap)
                    break
                    
            cap.release()
            cv2.destroyAllWindows()
            
        except Exception as e:
            print(f"摄像头拍照失败: {e}")
            traceback.print_exc()

    def _countdown_and_capture(self, cap):
        """倒计时并拍照"""
        import cv2
        import time
        
        # 3秒倒计时
        for i in range(3, 0, -1):
            start_time = time.time()
            
            while time.time() - start_time < 1:
                ret, frame = cap.read()
                if not ret:
                    return
                    
                # 显示倒计时
                cv2.putText(frame, str(i), (300, 300),
                          cv2.FONT_HERSHEY_SIMPLEX, 5, (0, 255, 0), 5)
                cv2.imshow('Animal Pose Matcher', frame)
                cv2.waitKey(1)
        
        # 拍照
        ret, frame = cap.read()
        if ret:
            # 保存图像
            img_path = self.retrieval_system.results_dir / "captured_image.jpg"
            cv2.imwrite(str(img_path), frame)
            print(f"图像已保存到: {img_path}")
            
            # 处理图像
            print("正在处理图像...")
            results = self.retrieval_system.retrieve_similar_poses(img_path)
            
            if results:
                print(f"找到匹配结果: {results[0]['category']} ({results[0]['similarity_score']*100:.1f}%)")
            else:
                print("未找到匹配结果")


def main():
    """主函数"""
    # 显示系统信息
    log_system_info()
    
    # 参数解析
    parser = argparse.ArgumentParser(description='动物姿态匹配器')
    parser.add_argument('--port', type=int, default=8080, help='Web服务器端口')
    parser.add_argument('--mode', choices=['web', 'camera'], default='web', 
                       help='运行模式: web (浏览器界面) 或 camera (摄像头界面)')
    
    try:
        args = parser.parse_args()
    except SystemExit:
        return 1
    
    try:
        if args.mode == 'web':
            # Web模式
            print("启动Web模式...")
            server = AnimalPoseMatcherServer(port=args.port)
            success = server.start()
            return 0 if success else 1
            
        else:
            # 摄像头模式
            print("启动摄像头模式...")
            retrieval_system = RetrievalSystem()
            camera_capture = CameraCapture(retrieval_system)
            camera_capture.capture_and_retrieve()
            return 0
            
    except Exception as e:
        print(f"程序运行出错: {e}")
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    sys.exit(main()) 